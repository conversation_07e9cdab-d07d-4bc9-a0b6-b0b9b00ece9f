// Package chat provides the UI components for AI chat interface
// Implements cyberpunk-styled chat interface with real-time AI interaction
package chat

import (
	"fmt"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// UI provides the user interface for AI chat
type UI struct {
	config *config.Config
	theme  *cybertheme.CyberTheme

	// Main components
	content         *container.Scroll
	chatHistory     *widget.List
	messageInput    *widget.Entry
	sendButton      *cybertheme.CyberButton
	contextDisplay  *fyne.Container
	statusLabel     *widget.Label

	// Chat state
	messages        []ChatMessage
	currentContext  []string
	isProcessing    bool
}

// ChatMessage represents a chat message
type ChatMessage struct {
	Content   string
	IsUser    bool
	Timestamp time.Time
	Status    string // "sent", "processing", "completed", "error"
}

// NewUI creates a new chat management UI
func NewUI(cfg *config.Config, theme *cybertheme.CyberTheme) *UI {
	ui := &UI{
		config:         cfg,
		theme:          theme,
		messages:       make([]ChatMessage, 0),
		currentContext: []string{"PostgreSQL", "Docker"},
		isProcessing:   false,
	}

	ui.createComponents()
	ui.addWelcomeMessage()
	return ui
}

// createComponents creates all UI components
func (ui *UI) createComponents() {
	// Create chat history list
	ui.createChatHistory()

	// Create message input
	ui.createMessageInput()

	// Create context display
	ui.createContextDisplay()

	// Create status label
	ui.statusLabel = widget.NewLabel(">>> AI ASSISTANT READY <<<")

	// Create main layout
	ui.createMainLayout()
}

// createChatHistory creates the chat history list
func (ui *UI) createChatHistory() {
	ui.chatHistory = widget.NewList(
		func() int {
			return len(ui.messages)
		},
		func() fyne.CanvasObject {
			return container.NewVBox(
				widget.NewLabel("Message content"),
				widget.NewLabel("Timestamp"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id >= len(ui.messages) {
				return
			}

			message := ui.messages[id]
			cont := obj.(*fyne.Container)
			contentLabel := cont.Objects[0].(*widget.Label)
			timestampLabel := cont.Objects[1].(*widget.Label)

			// Format message content
			prefix := "🤖 AI: "
			if message.IsUser {
				prefix = "👤 You: "
			}
			contentLabel.SetText(prefix + message.Content)
			
			// Style based on sender
			if message.IsUser {
				contentLabel.TextStyle = fyne.TextStyle{Bold: true}
			} else {
				contentLabel.TextStyle = fyne.TextStyle{}
			}

			// Format timestamp
			timestampLabel.SetText(message.Timestamp.Format("15:04:05"))
			timestampLabel.TextStyle = fyne.TextStyle{Monospace: true}
		},
	)
}

// createMessageInput creates the message input area
func (ui *UI) createMessageInput() {
	ui.messageInput = widget.NewMultiLineEntry()
	ui.messageInput.SetPlaceHolder("Type your message to the AI assistant...")
	ui.messageInput.Resize(fyne.NewSize(600, 100))

	// Handle Enter key to send message
	ui.messageInput.OnSubmitted = func(text string) {
		if !ui.isProcessing && text != "" {
			ui.sendMessage(text)
		}
	}

	ui.sendButton = cybertheme.NewCyberButtonWithVariant("📤 Send", cybertheme.ButtonPrimary, func() {
		if !ui.isProcessing && ui.messageInput.Text != "" {
			ui.sendMessage(ui.messageInput.Text)
		}
	})
}

// createContextDisplay creates the context information display
func (ui *UI) createContextDisplay() {
	contextLabel := widget.NewLabel("Current Context:")
	contextLabel.TextStyle = fyne.TextStyle{Bold: true}

	contextItems := container.NewHBox()
	for _, ctx := range ui.currentContext {
		badge := cybertheme.NewCyberButtonWithVariant(ctx, cybertheme.ButtonGhost, nil)
		badge.SetSize(cybertheme.ButtonSmall)
		contextItems.Add(badge)
	}

	clearBtn := cybertheme.NewCyberButtonWithVariant("🗑️ Clear", cybertheme.ButtonSecondary, ui.clearContext)
	clearBtn.SetSize(cybertheme.ButtonSmall)

	exportBtn := cybertheme.NewCyberButtonWithVariant("📤 Export", cybertheme.ButtonSecondary, ui.exportChat)
	exportBtn.SetSize(cybertheme.ButtonSmall)

	ui.contextDisplay = container.NewVBox(
		contextLabel,
		container.NewHBox(contextItems, clearBtn, exportBtn),
	)
}

// createMainLayout creates the main UI layout following fyne.md specifications
func (ui *UI) createMainLayout() {
	// Create header with context
	headerSection := container.NewVBox(
		widget.NewRichTextFromMarkdown(`# 💬 AI Chat Assistant

**NATURAL LANGUAGE INTERFACE**

> Intelligent conversation with context-aware AI assistant.
> Supports multi-turn conversations with memory and tool integration.

---`),
		ui.contextDisplay,
		widget.NewSeparator(),
	)

	// Create chat area (main content)
	chatArea := container.NewVBox(
		widget.NewLabel("💬 Chat History"),
		container.NewScroll(ui.chatHistory),
	)

	// Create input area
	inputActions := container.NewHBox(
		cybertheme.NewCyberButtonWithVariant("📎 Attach", cybertheme.ButtonSecondary, ui.attachFile),
		cybertheme.NewCyberButtonWithVariant("💻 Code", cybertheme.ButtonSecondary, ui.insertCode),
		cybertheme.NewCyberButtonWithVariant("🎤 Voice", cybertheme.ButtonSecondary, ui.voiceInput),
		widget.NewSeparator(),
		cybertheme.NewCyberButtonWithVariant("⚡ Shortcuts", cybertheme.ButtonGhost, ui.showShortcuts),
	)

	inputSection := container.NewVBox(
		widget.NewLabel("✍️ Message Input"),
		ui.messageInput,
		container.NewHBox(inputActions, ui.sendButton),
	)

	// Main layout
	mainContainer := container.NewVBox(
		headerSection,
		chatArea,
		widget.NewSeparator(),
		inputSection,
		widget.NewSeparator(),
		ui.statusLabel,
	)

	// Wrap in scroll container to handle overflow
	ui.content = container.NewScroll(mainContainer)
	ui.content.SetMinSize(fyne.NewSize(800, 600))
}

// addWelcomeMessage adds a welcome message to the chat
func (ui *UI) addWelcomeMessage() {
	welcomeMsg := ChatMessage{
		Content:   "Hello! I'm your AI development assistant. I can help with code analysis, debugging, optimization, and more. What would you like to work on today?",
		IsUser:    false,
		Timestamp: time.Now(),
		Status:    "completed",
	}
	ui.messages = append(ui.messages, welcomeMsg)
	ui.chatHistory.Refresh()
}

// sendMessage sends a user message and processes AI response
func (ui *UI) sendMessage(text string) {
	// Add user message
	userMsg := ChatMessage{
		Content:   text,
		IsUser:    true,
		Timestamp: time.Now(),
		Status:    "sent",
	}
	ui.messages = append(ui.messages, userMsg)

	// Clear input
	ui.messageInput.SetText("")

	// Set processing state
	ui.isProcessing = true
	ui.statusLabel.SetText(">>> AI is thinking... <<<")
	ui.sendButton.Disable()

	// Add processing message
	processingMsg := ChatMessage{
		Content:   "Processing your request...",
		IsUser:    false,
		Timestamp: time.Now(),
		Status:    "processing",
	}
	ui.messages = append(ui.messages, processingMsg)
	ui.chatHistory.Refresh()

	// Simulate AI processing (replace with actual AI integration)
	go func() {
		time.Sleep(2 * time.Second) // Simulate processing time

		// Use fyne.Do for thread-safe UI updates
		fyne.Do(func() {
			// Remove processing message
			ui.messages = ui.messages[:len(ui.messages)-1]

			// Add AI response
			aiResponse := ChatMessage{
				Content:   fmt.Sprintf("I understand you want to: %s\n\nLet me help you with that. Based on your current context (%v), I can provide specific assistance.", text, ui.currentContext),
				IsUser:    false,
				Timestamp: time.Now(),
				Status:    "completed",
			}
			ui.messages = append(ui.messages, aiResponse)

			// Reset state
			ui.isProcessing = false
			ui.statusLabel.SetText(">>> Ready for next message <<<")
			ui.sendButton.Enable()
			ui.chatHistory.Refresh()

			// Scroll to bottom
			ui.chatHistory.ScrollToBottom()
		})
	}()
}

// Event handlers
func (ui *UI) clearContext() {
	ui.currentContext = []string{}
	ui.createContextDisplay()
	ui.statusLabel.SetText(">>> Context cleared <<<")
}

func (ui *UI) exportChat() {
	// TODO: Implement chat export functionality
	ui.statusLabel.SetText(">>> Export feature coming soon <<<")
}

func (ui *UI) attachFile() {
	// TODO: Implement file attachment
	ui.statusLabel.SetText(">>> File attachment coming soon <<<")
}

func (ui *UI) insertCode() {
	// TODO: Implement code insertion helper
	ui.statusLabel.SetText(">>> Code insertion helper coming soon <<<")
}

func (ui *UI) voiceInput() {
	// TODO: Implement voice input
	ui.statusLabel.SetText(">>> Voice input coming soon <<<")
}

func (ui *UI) showShortcuts() {
	// TODO: Show keyboard shortcuts dialog
	ui.statusLabel.SetText(">>> Shortcuts: Ctrl+Enter to send, Ctrl+K for commands <<<")
}

// Content returns the UI content
func (ui *UI) Content() fyne.CanvasObject {
	return ui.content
}

// Refresh refreshes the UI
func (ui *UI) Refresh() {
	if ui.content != nil {
		ui.content.Refresh()
	}
}
