// Package docker - UI component for Docker module
// Implements the cyberpunk-themed interface for Docker container management
package docker

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// UI represents the Docker module UI
type UI struct {
	manager *Manager
	config  *config.Config
	theme   *cybertheme.CyberTheme

	// UI components
	content         *fyne.Container
	containerList   *widget.List
	containerInfo   *fyne.Container
	resourceUsage   *fyne.Container
	logsSection     *fyne.Container

	// Current state
	selectedContainer string
	currentFilter     string
}

// NewUI creates a new Docker UI component
func NewUI(manager *Manager, cfg *config.Config, theme *cybertheme.CyberTheme) *UI {
	ui := &UI{
		manager: manager,
		config:  cfg,
		theme:   theme,
	}

	ui.createContent()
	return ui
}

// createContent creates the main UI content following fyne.md specifications
func (ui *UI) createContent() {
	// Create search and filter header
	searchEntry := widget.NewEntry()
	searchEntry.SetPlaceHolder("Search containers...")

	filterSelect := widget.NewSelect([]string{"All", "Running", "Stopped", "Paused"}, func(value string) {
		ui.onFilterChanged(value)
	})
	filterSelect.SetSelected("All")

	addBtn := cybertheme.NewCyberButtonWithVariant("➕ New", cybertheme.ButtonPrimary, ui.onAddContainer)
	addBtn.SetSize(cybertheme.ButtonSmall)

	headerControls := container.NewHBox(
		widget.NewLabel("Search:"),
		searchEntry,
		widget.NewSeparator(),
		widget.NewLabel("Filter:"),
		filterSelect,
		widget.NewSeparator(),
		addBtn,
	)

	// Create left panel - Container List (40% width)
	leftPanelHeader := widget.NewLabel("🐳 Container List")
	leftPanelHeader.TextStyle = fyne.TextStyle{Bold: true}

	// Create container list
	containerList := ui.createContainerList()

	// Container actions
	containerActions := container.NewHBox(
		cybertheme.NewCyberButtonWithVariant("▶️ Start", cybertheme.ButtonPrimary, ui.onStartContainer),
		cybertheme.NewCyberButtonWithVariant("⏹️ Stop", cybertheme.ButtonSecondary, ui.onStopContainer),
		cybertheme.NewCyberButtonWithVariant("🔄 Restart", cybertheme.ButtonSecondary, ui.onRestartContainer),
		cybertheme.NewCyberButtonWithVariant("🗑️ Remove", cybertheme.ButtonDanger, ui.onRemoveContainer),
	)

	leftPanel := container.NewVBox(
		leftPanelHeader,
		widget.NewSeparator(),
		containerList,
		widget.NewSeparator(),
		containerActions,
	)

	// Wrap left panel in scroll container
	leftPanelScroll := container.NewScroll(leftPanel)
	leftPanelScroll.SetMinSize(fyne.NewSize(400, 500))

	// Create right panel - Container Details (60% width)
	rightPanelHeader := widget.NewLabel("📊 Container Details")
	rightPanelHeader.TextStyle = fyne.TextStyle{Bold: true}

	// Container info section
	containerInfo := ui.createContainerInfo()

	// Resource usage section
	resourceUsage := ui.createResourceUsage()

	// Logs section
	logsSection := ui.createLogsSection()

	rightPanel := container.NewVBox(
		rightPanelHeader,
		widget.NewSeparator(),
		containerInfo,
		widget.NewSeparator(),
		resourceUsage,
		widget.NewSeparator(),
		logsSection,
	)

	// Wrap right panel in scroll container
	rightPanelScroll := container.NewScroll(rightPanel)
	rightPanelScroll.SetMinSize(fyne.NewSize(600, 500))

	// Create main two-panel layout as specified in fyne.md (40% left, 60% right)
	mainPanels := container.NewHSplit(leftPanelScroll, rightPanelScroll)
	mainPanels.SetOffset(0.4) // 40% for left panel, 60% for right panel

	// Create complete layout with proper Material Design 3 spacing
	completeLayout := container.NewVBox(
		// Module header with proper spacing (SpaceLG = 24px)
		container.NewPadded(
			widget.NewRichTextFromMarkdown(`# 🐳 Docker Container Management

**ADVANCED CONTAINER ORCHESTRATION**

> Real-time container management with cyberpunk aesthetics.
> Monitor, control, and optimize your Docker infrastructure.

---`),
		),
		// Control header with SpaceMD spacing (16px)
		container.NewPadded(headerControls),
		// Main content panels
		mainPanels,
	)

	// Wrap in scroll container with bidirectional scrolling
	ui.content = container.NewScroll(completeLayout)
	ui.content.SetMinSize(fyne.NewSize(1000, 700))
	ui.content.Direction = container.ScrollBoth // Enable both horizontal and vertical scrolling
}

// Content returns the UI content
func (ui *UI) Content() fyne.CanvasObject {
	return container.NewScroll(ui.content)
}

// Refresh updates the UI content
func (ui *UI) Refresh() {
	if ui.content != nil {
		ui.content.Refresh()
	}
}

// createContainerList creates the container list widget
func (ui *UI) createContainerList() *widget.List {
	ui.containerList = widget.NewList(
		func() int {
			// TODO: Return actual container count
			return 5 // Placeholder
		},
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewLabel("●"), // Status indicator
				widget.NewLabel("Container Name"),
				widget.NewLabel("Image"),
				widget.NewLabel("Status"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			// TODO: Populate with actual container data
			cont := obj.(*fyne.Container)
			statusLabel := cont.Objects[0].(*widget.Label)
			nameLabel := cont.Objects[1].(*widget.Label)
			imageLabel := cont.Objects[2].(*widget.Label)
			statusTextLabel := cont.Objects[3].(*widget.Label)

			// Placeholder data
			containers := []struct {
				name, image, status, statusIcon string
			}{
				{"nginx-web", "nginx:latest", "Running", "🟢"},
				{"postgres-db", "postgres:14", "Running", "🟢"},
				{"redis-cache", "redis:alpine", "Stopped", "🔴"},
				{"app-backend", "myapp:v1.2", "Running", "🟢"},
				{"monitoring", "grafana:latest", "Paused", "🟡"},
			}

			if id < len(containers) {
				container := containers[id]
				statusLabel.SetText(container.statusIcon)
				nameLabel.SetText(container.name)
				nameLabel.TextStyle = fyne.TextStyle{Bold: true}
				imageLabel.SetText(container.image)
				statusTextLabel.SetText(container.status)
			}
		},
	)

	ui.containerList.OnSelected = func(id widget.ListItemID) {
		// TODO: Update with actual container selection
		containers := []string{"nginx-web", "postgres-db", "redis-cache", "app-backend", "monitoring"}
		if id < len(containers) {
			ui.selectedContainer = containers[id]
			ui.updateContainerDetails(ui.selectedContainer)
		}
	}

	return ui.containerList
}

// createContainerInfo creates the container information section
func (ui *UI) createContainerInfo() *fyne.Container {
	ui.containerInfo = container.NewVBox(
		widget.NewLabel("📋 Container Information"),
		widget.NewSeparator(),
		widget.NewLabel("Name: Select a container"),
		widget.NewLabel("Image: --"),
		widget.NewLabel("Status: --"),
		widget.NewLabel("Created: --"),
		widget.NewLabel("Ports: --"),
	)

	return ui.containerInfo
}

// createResourceUsage creates the resource usage section
func (ui *UI) createResourceUsage() *fyne.Container {
	cpuProgress := widget.NewProgressBar()
	cpuProgress.SetValue(0.45) // 45% placeholder

	memProgress := widget.NewProgressBar()
	memProgress.SetValue(0.72) // 72% placeholder

	diskProgress := widget.NewProgressBar()
	diskProgress.SetValue(0.34) // 34% placeholder

	ui.resourceUsage = container.NewVBox(
		widget.NewLabel("📊 Resource Usage"),
		widget.NewSeparator(),
		container.NewHBox(widget.NewLabel("CPU:"), cpuProgress, widget.NewLabel("45%")),
		container.NewHBox(widget.NewLabel("Memory:"), memProgress, widget.NewLabel("72%")),
		container.NewHBox(widget.NewLabel("Disk:"), diskProgress, widget.NewLabel("34%")),
	)

	return ui.resourceUsage
}

// createLogsSection creates the logs viewing section
func (ui *UI) createLogsSection() *fyne.Container {
	logsText := widget.NewMultiLineEntry()
	logsText.SetText("Container logs will appear here...\n[Live tail mode enabled]")
	logsText.Disable() // Read-only

	logsActions := container.NewHBox(
		cybertheme.NewCyberButtonWithVariant("📄 View Logs", cybertheme.ButtonSecondary, ui.onViewLogs),
		cybertheme.NewCyberButtonWithVariant("🔄 Refresh", cybertheme.ButtonSecondary, ui.onRefreshLogs),
		cybertheme.NewCyberButtonWithVariant("💾 Export", cybertheme.ButtonSecondary, ui.onExportLogs),
	)

	ui.logsSection = container.NewVBox(
		widget.NewLabel("📄 Container Logs"),
		widget.NewSeparator(),
		container.NewScroll(logsText),
		logsActions,
	)

	return ui.logsSection
}

// Event handlers
func (ui *UI) onFilterChanged(filter string) {
	ui.currentFilter = filter
	// TODO: Apply filter to container list
}

func (ui *UI) onAddContainer() {
	// TODO: Open container creation dialog
}

func (ui *UI) onStartContainer() {
	if ui.selectedContainer == "" {
		return
	}
	// TODO: Start selected container
}

func (ui *UI) onStopContainer() {
	if ui.selectedContainer == "" {
		return
	}
	// TODO: Stop selected container
}

func (ui *UI) onRestartContainer() {
	if ui.selectedContainer == "" {
		return
	}
	// TODO: Restart selected container
}

func (ui *UI) onRemoveContainer() {
	if ui.selectedContainer == "" {
		return
	}
	// TODO: Remove selected container with confirmation
}

func (ui *UI) onViewLogs() {
	// TODO: Open detailed logs view
}

func (ui *UI) onRefreshLogs() {
	// TODO: Refresh container logs
}

func (ui *UI) onExportLogs() {
	// TODO: Export logs to file
}

// updateContainerDetails updates the container details panel
func (ui *UI) updateContainerDetails(containerName string) {
	// TODO: Update with actual container details
	// This is a placeholder implementation
	ui.containerInfo.RemoveAll()
	ui.containerInfo.Add(widget.NewLabel("📋 Container Information"))
	ui.containerInfo.Add(widget.NewSeparator())
	ui.containerInfo.Add(widget.NewLabel("Name: " + containerName))
	ui.containerInfo.Add(widget.NewLabel("Image: nginx:latest"))
	ui.containerInfo.Add(widget.NewLabel("Status: Running"))
	ui.containerInfo.Add(widget.NewLabel("Created: 2 hours ago"))
	ui.containerInfo.Add(widget.NewLabel("Ports: 80:8080, 443:8443"))
	ui.containerInfo.Refresh()
}
