// Package search provides SearXNG search engine integration
// manager.go - Main search module manager
package search

import (
	"context"
	"fmt"
	"log/slog"
	"sync"
	"time"

	"fyne.io/fyne/v2"

	"assistant-go/internal/types"
	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// Manager handles search operations using SearXNG
type Manager struct {
	config *config.Config
	logger *slog.Logger

	// Search engine client
	client *SearXNGClient

	// Module state
	ctx     context.Context
	cancel  context.CancelFunc
	running bool
	mu      sync.RWMutex

	// Search history and caching
	history []SearchQuery
	cache   map[string]*SearchResult

	// UI component
	ui *UI
}

// SearchQuery represents a search query
type SearchQuery struct {
	Query      string    `json:"query"`
	Categories []string  `json:"categories"`
	Language   string    `json:"language"`
	Timestamp  time.Time `json:"timestamp"`
	ResultCount int      `json:"result_count"`
}

// SearchResult represents search results
type SearchResult struct {
	Query     string        `json:"query"`
	Results   []Result      `json:"results"`
	Engines   []string      `json:"engines"`
	Timestamp time.Time     `json:"timestamp"`
	Duration  time.Duration `json:"duration"`
	Total     int           `json:"total"`
}

// Result represents a single search result
type Result struct {
	Title       string    `json:"title"`
	URL         string    `json:"url"`
	Content     string    `json:"content"`
	Engine      string    `json:"engine"`
	Category    string    `json:"category"`
	Score       float64   `json:"score"`
	PublishedAt time.Time `json:"published_at,omitempty"`
	Thumbnail   string    `json:"thumbnail,omitempty"`
}

// NewManager creates a new search manager
func NewManager() *Manager {
	ctx, cancel := context.WithCancel(context.Background())

	return &Manager{
		ctx:     ctx,
		cancel:  cancel,
		logger:  slog.Default().With("component", "search"),
		history: make([]SearchQuery, 0),
		cache:   make(map[string]*SearchResult),
	}
}

// Initialize implements the Module interface
func (m *Manager) Initialize(ctx context.Context, cfg *config.Config) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.config = cfg
	m.logger.Info("Initializing search module")

	if !cfg.Search.Enabled {
		m.logger.Info("Search module disabled in configuration")
		return nil
	}

	// Initialize SearXNG client
	client, err := NewSearXNGClient(cfg.Search)
	if err != nil {
		return fmt.Errorf("failed to initialize SearXNG client: %w", err)
	}

	m.client = client

	// Test connectivity
	if err := m.client.TestConnection(); err != nil {
		m.logger.Warn("SearXNG connectivity test failed", "error", err)
		// Don't fail initialization, just log the warning
	}

	m.logger.Info("Search module initialized successfully")
	return nil
}

// Start implements the Module interface
func (m *Manager) Start(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.running {
		return fmt.Errorf("search module is already running")
	}

	m.logger.Info("Starting search module")

	if m.client != nil {
		if err := m.client.Start(); err != nil {
			return fmt.Errorf("failed to start SearXNG client: %w", err)
		}
	}

	m.running = true
	m.logger.Info("Search module started successfully")
	return nil
}

// Stop implements the Module interface
func (m *Manager) Stop(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.running {
		return nil
	}

	m.logger.Info("Stopping search module")

	if m.client != nil {
		m.client.Stop()
	}

	m.cancel()
	m.running = false

	m.logger.Info("Search module stopped")
	return nil
}

// Health implements the Module interface
func (m *Manager) Health() types.ModuleHealth {
	m.mu.RLock()
	defer m.mu.RUnlock()

	health := types.ModuleHealth{
		Status:    "healthy",
		Message:   "Search engine available",
		LastCheck: time.Now(),
	}

	if !m.running {
		health.Status = "unhealthy"
		health.Message = "Module not running"
		return health
	}

	if m.client == nil {
		health.Status = "unhealthy"
		health.Message = "Search client not initialized"
		return health
	}

	// Check SearXNG connectivity
	if err := m.client.TestConnection(); err != nil {
		health.Status = "degraded"
		health.Message = fmt.Sprintf("Search engine connectivity issues: %v", err)
		return health
	}

	// Add search statistics
	historyCount := len(m.history)
	cacheCount := len(m.cache)
	health.Message = fmt.Sprintf("Search engine ready (%d queries, %d cached)", historyCount, cacheCount)

	return health
}

// Name implements the Module interface
func (m *Manager) Name() string {
	return "search"
}

// Search performs a search query
func (m *Manager) Search(query string, categories []string) (*SearchResult, error) {
	if m.client == nil {
		return nil, fmt.Errorf("search client not initialized")
	}

	// Check cache first
	cacheKey := fmt.Sprintf("%s:%v", query, categories)
	if cached, exists := m.cache[cacheKey]; exists {
		// Return cached result if it's less than 1 hour old
		if time.Since(cached.Timestamp) < time.Hour {
			return cached, nil
		}
		// Remove expired cache entry
		delete(m.cache, cacheKey)
	}

	start := time.Now()
	result, err := m.client.Search(query, categories)
	if err != nil {
		return nil, fmt.Errorf("search failed: %w", err)
	}

	result.Duration = time.Since(start)

	// Add to history
	m.addToHistory(SearchQuery{
		Query:       query,
		Categories:  categories,
		Language:    m.config.Search.Language,
		Timestamp:   time.Now(),
		ResultCount: len(result.Results),
	})

	// Cache the result
	m.cache[cacheKey] = result

	return result, nil
}

// SearchWithOptions performs a search with custom options
func (m *Manager) SearchWithOptions(query string, options SearchOptions) (*SearchResult, error) {
	if m.client == nil {
		return nil, fmt.Errorf("search client not initialized")
	}

	return m.client.SearchWithOptions(query, options)
}

// GetSuggestions gets search suggestions for a query
func (m *Manager) GetSuggestions(query string) ([]string, error) {
	if m.client == nil {
		return nil, fmt.Errorf("search client not initialized")
	}

	return m.client.GetSuggestions(query)
}

// GetHistory returns search history
func (m *Manager) GetHistory() []SearchQuery {
	m.mu.RLock()
	defer m.mu.RUnlock()

	// Return a copy to prevent external modification
	history := make([]SearchQuery, len(m.history))
	copy(history, m.history)
	return history
}

// ClearHistory clears search history
func (m *Manager) ClearHistory() {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.history = make([]SearchQuery, 0)
	m.logger.Info("Search history cleared")
}

// ClearCache clears search result cache
func (m *Manager) ClearCache() {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.cache = make(map[string]*SearchResult)
	m.logger.Info("Search cache cleared")
}

// GetEngines returns available search engines
func (m *Manager) GetEngines() ([]string, error) {
	if m.client == nil {
		return nil, fmt.Errorf("search client not initialized")
	}

	return m.client.GetEngines()
}

// GetCategories returns available search categories
func (m *Manager) GetCategories() ([]string, error) {
	if m.client == nil {
		return nil, fmt.Errorf("search client not initialized")
	}

	return m.client.GetCategories()
}

// addToHistory adds a query to search history
func (m *Manager) addToHistory(query SearchQuery) {
	m.mu.Lock()
	defer m.mu.Unlock()

	// Add to beginning of history
	m.history = append([]SearchQuery{query}, m.history...)

	// Keep only last 100 queries
	if len(m.history) > 100 {
		m.history = m.history[:100]
	}
}

// GetStats returns search statistics
func (m *Manager) GetStats() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()

	return map[string]interface{}{
		"total_queries":   len(m.history),
		"cached_results":  len(m.cache),
		"engine_status":   m.client != nil,
		"last_query":      m.getLastQuery(),
		"popular_categories": m.getPopularCategories(),
	}
}

// getLastQuery returns the last search query
func (m *Manager) getLastQuery() string {
	if len(m.history) > 0 {
		return m.history[0].Query
	}
	return ""
}

// getPopularCategories returns the most used categories
func (m *Manager) getPopularCategories() []string {
	categoryCount := make(map[string]int)
	for _, query := range m.history {
		for _, cat := range query.Categories {
			categoryCount[cat]++
		}
	}

	// Return top 5 categories
	popular := make([]string, 0, 5)
	for cat := range categoryCount {
		popular = append(popular, cat)
		if len(popular) >= 5 {
			break
		}
	}

	return popular
}

// GetUI returns the UI component for the search module
func (m *Manager) GetUI() fyne.CanvasObject {
	if m.ui == nil {
		// Create cyberpunk theme
		theme := cybertheme.NewCyberTheme()

		// Initialize UI component
		m.ui = NewUI(m, m.config, theme)
	}

	return m.ui.Content()
}

// TODO: Implement search result ranking and filtering
// TODO: Implement search result export functionality
// TODO: Implement custom search engines configuration
// TODO: Implement search analytics and insights
// TODO: Implement AI-powered search result summarization
// TODO: Implement search result clustering and categorization
