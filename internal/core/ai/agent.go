// Package ai provides AI agent implementation
// agent.go - Core AI agent functionality
package ai

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"github.com/tmc/langchaingo/llms"
	"github.com/tmc/langchaingo/memory"
)

// Agent represents an AI agent that can perform tasks and have conversations
type Agent struct {
	llm    llms.Model
	memory memory.ConversationBuffer
	logger *slog.Logger

	// Agent capabilities
	tools     map[string]Tool
	running   bool
}

// Tool represents a tool that the agent can use
type Tool interface {
	Name() string
	Description() string
	Execute(ctx context.Context, input string) (string, error)
}

// TaskPlan represents a plan for executing a task
type TaskPlan struct {
	ID          string      `json:"id"`
	Description string      `json:"description"`
	Steps       []TaskStep  `json:"steps"`
	Estimated   time.Duration `json:"estimated_duration"`
	Created     time.Time   `json:"created"`
}

// TaskStep represents a single step in a task plan
type TaskStep struct {
	ID          string                 `json:"id"`
	Description string                 `json:"description"`
	Tool        string                 `json:"tool,omitempty"`
	Input       string                 `json:"input,omitempty"`
	Expected    string                 `json:"expected_output,omitempty"`
	Dependencies []string              `json:"dependencies,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// NewAgent creates a new AI agent
func NewAgent(llm llms.Model, memory memory.ConversationBuffer) (*Agent, error) {
	if llm == nil {
		return nil, fmt.Errorf("LLM model is required")
	}

	agent := &Agent{
		llm:    llm,
		memory: memory,
		logger: slog.Default().With("component", "ai-agent"),
		tools:  make(map[string]Tool),
	}

	// Initialize built-in tools
	agent.initializeTools()

	return agent, nil
}

// Start starts the AI agent
func (a *Agent) Start() error {
	a.running = true
	a.logger.Info("AI agent started")
	return nil
}

// Stop stops the AI agent
func (a *Agent) Stop() {
	a.running = false
	a.logger.Info("AI agent stopped")
}

// Chat handles a conversation with the agent
func (a *Agent) Chat(message string, chatContext map[string]interface{}) (string, error) {
	if !a.running {
		return "", fmt.Errorf("agent is not running")
	}

	// Create context for the LLM call
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	// Build the prompt with context
	prompt := a.buildChatPrompt(message, chatContext)

	// Generate response
	response, err := llms.GenerateFromSinglePrompt(ctx, a.llm, prompt)
	if err != nil {
		return "", fmt.Errorf("failed to generate response: %w", err)
	}

	// Store in memory
	a.memory.ChatHistory.AddUserMessage(ctx, message)
	a.memory.ChatHistory.AddAIMessage(ctx, response)

	a.logger.Debug("Chat response generated", "input_length", len(message), "output_length", len(response))
	return response, nil
}

// ExecuteTask executes a task using the agent's capabilities
func (a *Agent) ExecuteTask(request TaskRequest) (*TaskResponse, error) {
	if !a.running {
		return nil, fmt.Errorf("agent is not running")
	}

	a.logger.Info("Executing task", "id", request.ID, "description", request.Description)

	// Create task plan
	plan, err := a.createTaskPlan(request)
	if err != nil {
		return nil, fmt.Errorf("failed to create task plan: %w", err)
	}

	// Execute the plan
	result, actions, err := a.executePlan(plan, request.Context)
	if err != nil {
		return &TaskResponse{
			ID:      fmt.Sprintf("resp_%d", time.Now().Unix()),
			TaskID:  request.ID,
			Status:  "failed",
			Result:  fmt.Sprintf("Task execution failed: %v", err),
			Actions: actions,
			Created: time.Now(),
		}, nil
	}

	response := &TaskResponse{
		ID:      fmt.Sprintf("resp_%d", time.Now().Unix()),
		TaskID:  request.ID,
		Status:  "completed",
		Result:  result,
		Actions: actions,
		Metadata: map[string]interface{}{
			"plan_id": plan.ID,
			"steps_executed": len(plan.Steps),
		},
		Created: time.Now(),
	}

	a.logger.Info("Task completed", "id", request.ID, "status", response.Status)
	return response, nil
}

// AddTool adds a tool to the agent's toolkit
func (a *Agent) AddTool(tool Tool) {
	a.tools[tool.Name()] = tool
	a.logger.Info("Tool added to agent", "tool", tool.Name())
}

// RemoveTool removes a tool from the agent's toolkit
func (a *Agent) RemoveTool(name string) {
	delete(a.tools, name)
	a.logger.Info("Tool removed from agent", "tool", name)
}

// ListTools returns the list of available tools
func (a *Agent) ListTools() []string {
	tools := make([]string, 0, len(a.tools))
	for name := range a.tools {
		tools = append(tools, name)
	}
	return tools
}

// initializeTools initializes built-in tools
func (a *Agent) initializeTools() {
	// Add basic tools
	a.AddTool(&SearchTool{})
	a.AddTool(&CodeAnalysisTool{})
	a.AddTool(&DatabaseQueryTool{})
	a.AddTool(&KubernetesTool{})
	a.AddTool(&DockerTool{})
}

// buildChatPrompt builds a prompt for chat interactions
func (a *Agent) buildChatPrompt(message string, chatContext map[string]interface{}) string {
	prompt := `You are an intelligent development assistant with access to various tools and systems.
You help developers with their daily tasks including code analysis, database operations,
Kubernetes management, Docker operations, and general development questions.

Available tools: ` + fmt.Sprintf("%v", a.ListTools()) + `

Context: ` + fmt.Sprintf("%v", chatContext) + `

User message: ` + message + `

Please provide a helpful and accurate response. If you need to use any tools,
explain what you're doing and why.`

	return prompt
}

// createTaskPlan creates a plan for executing a task
func (a *Agent) createTaskPlan(request TaskRequest) (*TaskPlan, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Use LLM to create a task plan
	planPrompt := fmt.Sprintf(`Create a detailed plan to execute the following task:

Task: %s
Context: %v

Available tools: %v

Please break down the task into specific steps that can be executed using the available tools.
Each step should be clear and actionable.

Respond with a structured plan including:
1. Step description
2. Tool to use (if any)
3. Expected input/output
4. Dependencies on other steps`,
		request.Description, request.Context, a.ListTools())

	response, err := llms.GenerateFromSinglePrompt(ctx, a.llm, planPrompt)
	if err != nil {
		return nil, fmt.Errorf("failed to generate task plan: %w", err)
	}

	// Parse the response into a structured plan
	// For now, create a simple plan structure
	plan := &TaskPlan{
		ID:          fmt.Sprintf("plan_%d", time.Now().Unix()),
		Description: request.Description,
		Steps:       a.parseTaskSteps(response),
		Estimated:   5 * time.Minute, // Default estimation
		Created:     time.Now(),
	}

	return plan, nil
}

// parseTaskSteps parses the LLM response into task steps
func (a *Agent) parseTaskSteps(response string) []TaskStep {
	// This is a simplified implementation
	// In a real implementation, you would parse the structured response
	return []TaskStep{
		{
			ID:          "step_1",
			Description: "Analyze the task requirements",
			Tool:        "",
			Input:       response,
			Expected:    "Task analysis complete",
		},
		{
			ID:          "step_2",
			Description: "Execute the main task",
			Tool:        "search",
			Input:       "relevant information",
			Expected:    "Task completed successfully",
			Dependencies: []string{"step_1"},
		},
	}
}

// executePlan executes a task plan
func (a *Agent) executePlan(plan *TaskPlan, planContext map[string]interface{}) (string, []string, error) {
	var results []string
	var actions []string

	for _, step := range plan.Steps {
		a.logger.Debug("Executing step", "step", step.ID, "description", step.Description)

		stepResult, err := a.executeStep(step, planContext)
		if err != nil {
			return "", actions, fmt.Errorf("step %s failed: %w", step.ID, err)
		}

		results = append(results, stepResult)
		actions = append(actions, fmt.Sprintf("Executed: %s", step.Description))
	}

	finalResult := fmt.Sprintf("Task completed successfully. Steps executed:\n%s",
		fmt.Sprintf("- %s", results))

	return finalResult, actions, nil
}

// executeStep executes a single task step
func (a *Agent) executeStep(step TaskStep, stepContext map[string]interface{}) (string, error) {
	if step.Tool == "" {
		// No tool required, just return the description
		return step.Description, nil
	}

	tool, exists := a.tools[step.Tool]
	if !exists {
		return "", fmt.Errorf("tool not found: %s", step.Tool)
	}

	toolCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	result, err := tool.Execute(toolCtx, step.Input)
	if err != nil {
		return "", fmt.Errorf("tool execution failed: %w", err)
	}

	return result, nil
}

// GetMemory returns the agent's conversation memory
func (a *Agent) GetMemory() memory.ConversationBuffer {
	return a.memory
}

// ClearMemory clears the agent's conversation memory
func (a *Agent) ClearMemory() {
	a.memory = *memory.NewConversationBuffer()
	a.logger.Info("Agent memory cleared")
}

// TODO: Implement advanced reasoning capabilities
// TODO: Implement tool chaining and workflow automation
// TODO: Implement learning from user feedback
// TODO: Implement context-aware tool selection
// TODO: Implement multi-step task execution with rollback
// TODO: Implement agent collaboration and delegation
