// Package tasks provides task management functionality for <PERSON><PERSON><PERSON> and Taskfile
// Implements direct reading and execution of build tasks with favorites management
package tasks

import (
	"bufio"
	"context"
	"fmt"
	"log/slog"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"
	"sync"
	"time"

	"fyne.io/fyne/v2"

	"assistant-go/internal/config"
	"assistant-go/internal/types"
	"assistant-go/pkg/cybertheme"
)

// Manager handles task management operations
type Manager struct {
	config *config.Config
	logger *slog.Logger

	// Task management
	makefileTasks map[string]*Task
	taskfileTasks map[string]*Task
	favorites     []string
	mu            sync.RWMutex

	// Module state
	ctx     context.Context
	cancel  context.CancelFunc
	running bool

	// File watchers
	makefileWatcher *FileWatcher
	taskfileWatcher *FileWatcher

	// UI component
	ui *UI
}

// Task represents a build task
type Task struct {
	Name        string
	Description string
	Source      string // "makefile" or "taskfile"
	Command     string
	Dependencies []string
	LastRun     time.Time
	RunCount    int
}

// TaskExecution represents a task execution result
type TaskExecution struct {
	Task      *Task
	StartTime time.Time
	EndTime   time.Time
	Duration  time.Duration
	Output    string
	Error     error
	ExitCode  int
}

// FileWatcher watches for file changes
type FileWatcher struct {
	path     string
	lastMod  time.Time
	callback func()
}

// NewManager creates a new task manager
func NewManager() *Manager {
	ctx, cancel := context.WithCancel(context.Background())

	return &Manager{
		makefileTasks: make(map[string]*Task),
		taskfileTasks: make(map[string]*Task),
		favorites:     make([]string, 0),
		ctx:           ctx,
		cancel:        cancel,
		logger:        slog.Default().With("component", "tasks"),
	}
}

// Initialize implements the Module interface
func (m *Manager) Initialize(ctx context.Context, cfg *config.Config) error {
	m.config = cfg
	m.logger.Info("Initializing task manager")

	// Set favorites from config
	m.favorites = cfg.Tasks.Favorites

	// Load tasks from files
	if err := m.loadTasks(); err != nil {
		m.logger.Error("Failed to load tasks", "error", err)
		// Continue initialization - tasks can be loaded later
	}

	// Setup file watchers if auto-detect is enabled
	if cfg.Tasks.AutoDetect {
		m.setupFileWatchers()
	}

	m.logger.Info("Task manager initialized",
		"makefile_tasks", len(m.makefileTasks),
		"taskfile_tasks", len(m.taskfileTasks))
	return nil
}

// Start implements the Module interface
func (m *Manager) Start(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.running {
		return fmt.Errorf("task manager is already running")
	}

	m.logger.Info("Starting task manager")

	// Start file watchers
	if m.makefileWatcher != nil {
		go m.makefileWatcher.Watch(m.ctx)
	}
	if m.taskfileWatcher != nil {
		go m.taskfileWatcher.Watch(m.ctx)
	}

	m.running = true
	m.logger.Info("Task manager started successfully")
	return nil
}

// Stop implements the Module interface
func (m *Manager) Stop(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.running {
		return nil
	}

	m.logger.Info("Stopping task manager")

	// Cancel context to stop watchers
	m.cancel()

	m.running = false
	m.logger.Info("Task manager stopped")
	return nil
}

// Name implements the Module interface
func (m *Manager) Name() string {
	return "tasks"
}

// Health implements the Module interface
func (m *Manager) Health() types.ModuleHealth {
	m.mu.RLock()
	defer m.mu.RUnlock()

	health := types.ModuleHealth{
		Status:    "healthy",
		Message:   fmt.Sprintf("%d makefile + %d taskfile tasks", len(m.makefileTasks), len(m.taskfileTasks)),
		LastCheck: time.Now(),
	}

	// Check if task files exist
	makefileExists := m.fileExists(m.config.Tasks.MakefilePath)
	taskfileExists := m.fileExists(m.config.Tasks.TaskfilePath)

	if !makefileExists && !taskfileExists {
		health.Status = "degraded"
		health.Message = "No task files found"
	} else if len(m.favorites) > 0 {
		health.Message += fmt.Sprintf(" (%d favorites)", len(m.favorites))
	}

	if !m.running {
		health.Status = "unhealthy"
		health.Message = "Module not running"
	}

	return health
}

// loadTasks loads tasks from Makefile and Taskfile
func (m *Manager) loadTasks() error {
	// Load Makefile tasks
	if m.fileExists(m.config.Tasks.MakefilePath) {
		if err := m.loadMakefileTasks(); err != nil {
			m.logger.Error("Failed to load Makefile tasks", "error", err)
		}
	}

	// Load Taskfile tasks
	if m.fileExists(m.config.Tasks.TaskfilePath) {
		if err := m.loadTaskfileTasks(); err != nil {
			m.logger.Error("Failed to load Taskfile tasks", "error", err)
		}
	}

	return nil
}

// loadMakefileTasks loads tasks from Makefile
func (m *Manager) loadMakefileTasks() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	file, err := os.Open(m.config.Tasks.MakefilePath)
	if err != nil {
		return fmt.Errorf("failed to open Makefile: %w", err)
	}
	defer file.Close()

	// Clear existing tasks
	m.makefileTasks = make(map[string]*Task)

	scanner := bufio.NewScanner(file)
	targetRegex := regexp.MustCompile(`^([a-zA-Z0-9_-]+):\s*(.*)$`)
	commentRegex := regexp.MustCompile(`^#\s*(.*)$`)

	var currentComment string

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		// Skip empty lines
		if line == "" {
			currentComment = ""
			continue
		}

		// Check for comments
		if matches := commentRegex.FindStringSubmatch(line); matches != nil {
			currentComment = matches[1]
			continue
		}

		// Check for targets
		if matches := targetRegex.FindStringSubmatch(line); matches != nil {
			taskName := matches[1]
			dependencies := strings.Fields(matches[2])

			// Skip special targets
			if strings.HasPrefix(taskName, ".") {
				continue
			}

			task := &Task{
				Name:         taskName,
				Description:  currentComment,
				Source:       "makefile",
				Command:      fmt.Sprintf("make %s", taskName),
				Dependencies: dependencies,
			}

			m.makefileTasks[taskName] = task
			currentComment = ""
		}
	}

	m.logger.Info("Loaded Makefile tasks", "count", len(m.makefileTasks))
	return scanner.Err()
}

// loadTaskfileTasks loads tasks from Taskfile
func (m *Manager) loadTaskfileTasks() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// For now, implement basic YAML parsing
	// In a full implementation, we'd use a proper YAML parser
	file, err := os.Open(m.config.Tasks.TaskfilePath)
	if err != nil {
		return fmt.Errorf("failed to open Taskfile: %w", err)
	}
	defer file.Close()

	// Clear existing tasks
	m.taskfileTasks = make(map[string]*Task)

	scanner := bufio.NewScanner(file)
	taskRegex := regexp.MustCompile(`^\s*([a-zA-Z0-9_-]+):\s*$`)
	descRegex := regexp.MustCompile(`^\s*desc:\s*(.*)$`)

	var currentTask string
	var currentDesc string

	for scanner.Scan() {
		line := scanner.Text()

		// Check for task definition
		if matches := taskRegex.FindStringSubmatch(line); matches != nil {
			// Save previous task if exists
			if currentTask != "" {
				task := &Task{
					Name:        currentTask,
					Description: currentDesc,
					Source:      "taskfile",
					Command:     fmt.Sprintf("task %s", currentTask),
				}
				m.taskfileTasks[currentTask] = task
			}

			currentTask = matches[1]
			currentDesc = ""
			continue
		}

		// Check for description
		if matches := descRegex.FindStringSubmatch(line); matches != nil {
			currentDesc = matches[1]
		}
	}

	// Save last task
	if currentTask != "" {
		task := &Task{
			Name:        currentTask,
			Description: currentDesc,
			Source:      "taskfile",
			Command:     fmt.Sprintf("task %s", currentTask),
		}
		m.taskfileTasks[currentTask] = task
	}

	m.logger.Info("Loaded Taskfile tasks", "count", len(m.taskfileTasks))
	return scanner.Err()
}

// setupFileWatchers sets up file watchers for auto-reload
func (m *Manager) setupFileWatchers() {
	// Setup Makefile watcher
	if m.fileExists(m.config.Tasks.MakefilePath) {
		m.makefileWatcher = &FileWatcher{
			path: m.config.Tasks.MakefilePath,
			callback: func() {
				m.logger.Info("Makefile changed, reloading tasks")
				m.loadMakefileTasks()
			},
		}
	}

	// Setup Taskfile watcher
	if m.fileExists(m.config.Tasks.TaskfilePath) {
		m.taskfileWatcher = &FileWatcher{
			path: m.config.Tasks.TaskfilePath,
			callback: func() {
				m.logger.Info("Taskfile changed, reloading tasks")
				m.loadTaskfileTasks()
			},
		}
	}
}

// GetAllTasks returns all available tasks
func (m *Manager) GetAllTasks() map[string]*Task {
	m.mu.RLock()
	defer m.mu.RUnlock()

	allTasks := make(map[string]*Task)

	// Add Makefile tasks
	for name, task := range m.makefileTasks {
		allTasks[name] = task
	}

	// Add Taskfile tasks
	for name, task := range m.taskfileTasks {
		allTasks[name] = task
	}

	return allTasks
}

// GetTask returns a specific task by name
func (m *Manager) GetTask(name string) (*Task, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	// Check Makefile tasks first
	if task, exists := m.makefileTasks[name]; exists {
		return task, nil
	}

	// Check Taskfile tasks
	if task, exists := m.taskfileTasks[name]; exists {
		return task, nil
	}

	return nil, fmt.Errorf("task %s not found", name)
}

// ExecuteTask executes a task and returns the result
func (m *Manager) ExecuteTask(name string) (*TaskExecution, error) {
	task, err := m.GetTask(name)
	if err != nil {
		return nil, err
	}

	execution := &TaskExecution{
		Task:      task,
		StartTime: time.Now(),
	}

	// Parse command
	parts := strings.Fields(task.Command)
	if len(parts) == 0 {
		execution.Error = fmt.Errorf("empty command")
		execution.EndTime = time.Now()
		execution.Duration = execution.EndTime.Sub(execution.StartTime)
		return execution, execution.Error
	}

	// Execute command
	cmd := exec.Command(parts[0], parts[1:]...)
	cmd.Dir = filepath.Dir(m.config.Tasks.MakefilePath) // Use project directory

	output, err := cmd.CombinedOutput()
	execution.EndTime = time.Now()
	execution.Duration = execution.EndTime.Sub(execution.StartTime)
	execution.Output = string(output)
	execution.Error = err

	if cmd.ProcessState != nil {
		execution.ExitCode = cmd.ProcessState.ExitCode()
	}

	// Update task statistics
	m.mu.Lock()
	task.LastRun = execution.StartTime
	task.RunCount++
	m.mu.Unlock()

	m.logger.Info("Task executed",
		"name", name,
		"duration", execution.Duration,
		"exit_code", execution.ExitCode)

	return execution, nil
}

// GetFavorites returns the list of favorite tasks
func (m *Manager) GetFavorites() []string {
	m.mu.RLock()
	defer m.mu.RUnlock()

	// Return a copy
	favorites := make([]string, len(m.favorites))
	copy(favorites, m.favorites)
	return favorites
}

// AddFavorite adds a task to favorites
func (m *Manager) AddFavorite(taskName string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// Check if task exists
	if _, err := m.GetTask(taskName); err != nil {
		return err
	}

	// Check if already in favorites
	for _, fav := range m.favorites {
		if fav == taskName {
			return fmt.Errorf("task %s is already in favorites", taskName)
		}
	}

	m.favorites = append(m.favorites, taskName)
	m.logger.Info("Task added to favorites", "name", taskName)
	return nil
}

// RemoveFavorite removes a task from favorites
func (m *Manager) RemoveFavorite(taskName string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	for i, fav := range m.favorites {
		if fav == taskName {
			m.favorites = append(m.favorites[:i], m.favorites[i+1:]...)
			m.logger.Info("Task removed from favorites", "name", taskName)
			return nil
		}
	}

	return fmt.Errorf("task %s is not in favorites", taskName)
}

// ReloadTasks reloads tasks from files
func (m *Manager) ReloadTasks() error {
	m.logger.Info("Reloading tasks")
	return m.loadTasks()
}

// fileExists checks if a file exists
func (m *Manager) fileExists(path string) bool {
	_, err := os.Stat(path)
	return err == nil
}

// Watch watches for file changes
func (fw *FileWatcher) Watch(ctx context.Context) {
	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()

	// Get initial modification time
	if stat, err := os.Stat(fw.path); err == nil {
		fw.lastMod = stat.ModTime()
	}

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			if stat, err := os.Stat(fw.path); err == nil {
				if stat.ModTime().After(fw.lastMod) {
					fw.lastMod = stat.ModTime()
					if fw.callback != nil {
						fw.callback()
					}
				}
			}
		}
	}
}

// GetUI returns the UI component for the tasks module
func (m *Manager) GetUI() fyne.CanvasObject {
	if m.ui == nil {
		// Create cyberpunk theme
		theme := cybertheme.NewCyberTheme()

		// Initialize UI component
		m.ui = NewUI(m, m.config, theme)
	}

	return m.ui.Content()
}
