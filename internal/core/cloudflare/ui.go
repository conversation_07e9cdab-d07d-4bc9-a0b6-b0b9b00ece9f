// Package cloudflare - UI component for Cloudflare module
// Implements the cyberpunk-themed interface for Cloudflare management
package cloudflare

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// UI represents the Cloudflare module UI
type UI struct {
	manager *Manager
	config  *config.Config
	theme   *cybertheme.CyberTheme

	// UI components
	content *fyne.Container
}

// NewUI creates a new Cloudflare UI component
func NewUI(manager *Manager, cfg *config.Config, theme *cybertheme.CyberTheme) *UI {
	ui := &UI{
		manager: manager,
		config:  cfg,
		theme:   theme,
	}

	ui.createContent()
	return ui
}

// createContent creates the main UI content
func (ui *UI) createContent() {
	// Create main content with cyberpunk styling
	title := widget.NewCard(
		"☁️ Cloudflare Management",
		"Advanced cloud infrastructure with cyberpunk aesthetics",
		widget.NewLabel("Manage Cloudflare zones, DNS, tunnels, and security settings with style."),
	)

	// Cloudflare status
	statusCard := widget.NewCard(
		"Cloudflare Status",
		"Current account information",
		container.NewVBox(
			widget.NewLabel("Status: Connected"),
			widget.NewLabel("Zones: 3 Active"),
			widget.NewLabel("DNS Records: 47"),
			widget.NewLabel("Tunnels: 2 Running"),
			widget.NewProgressBarInfinite(),
		),
	)

	// Quick actions
	actionsCard := widget.NewCard(
		"Quick Actions",
		"Common Cloudflare operations",
		container.NewVBox(
			cybertheme.NewCyberButton("🌐 Manage Zones", func() {
				// TODO: Open zone manager
			}),
			cybertheme.NewCyberButton("📡 DNS Records", func() {
				// TODO: Open DNS manager
			}),
			cybertheme.NewCyberButton("🔒 Security Rules", func() {
				// TODO: Open security manager
			}),
			cybertheme.NewCyberButton("🚇 Tunnel Config", func() {
				// TODO: Open tunnel manager
			}),
		),
	)

	// Recent activities
	recentCard := widget.NewCard(
		"Recent Activities",
		"Latest Cloudflare operations",
		container.NewVBox(
			widget.NewLabel("📡 DNS record api.example.com updated"),
			widget.NewLabel("🚇 Tunnel home-tunnel connected"),
			widget.NewLabel("🔒 Security rule 'Block Bad IPs' enabled"),
		),
	)

	// Main layout
	ui.content = container.NewVBox(
		title,
		container.NewGridWithColumns(2,
			statusCard,
			actionsCard,
		),
		recentCard,
		widget.NewCard(
			"🚧 Under Development",
			"This module is being enhanced",
			widget.NewRichTextFromMarkdown(`
## Cloudflare Module Features

This module will provide comprehensive Cloudflare management capabilities:

### Core Features:
- **Zone Management**: Create, configure, and manage DNS zones
- **DNS Management**: Add, update, and delete DNS records with real-time validation
- **Tunnel Management**: Configure and monitor Cloudflare tunnels
- **Security Rules**: Manage firewall rules, rate limiting, and access policies
- **CDN Configuration**: Cache settings, page rules, and performance optimization

### Advanced Features:
- **Analytics Dashboard**: Real-time traffic and security analytics
- **SSL/TLS Management**: Certificate management and security settings
- **Workers Management**: Deploy and manage Cloudflare Workers
- **Load Balancing**: Configure and monitor load balancers
- **API Integration**: Full Cloudflare API v4 integration

### Cyberpunk Aesthetics:
- Material Design 3 compliance with blue-dominant color scheme
- Real-time status indicators with animated elements
- Responsive layout with proper scrolling support
- Pure white bold text for maximum contrast and readability

Stay tuned for the full implementation!
			`),
		),
	)
}

// Content returns the UI content
func (ui *UI) Content() fyne.CanvasObject {
	return container.NewScroll(ui.content)
}

// Refresh updates the UI content
func (ui *UI) Refresh() {
	if ui.content != nil {
		ui.content.Refresh()
	}
}
