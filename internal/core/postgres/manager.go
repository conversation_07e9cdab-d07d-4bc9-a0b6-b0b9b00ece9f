// Package postgres provides PostgreSQL database management functionality
// Implements the manager.go/ui.go structure as specified in the architecture
package postgres

import (
	"context"
	"fmt"
	"log/slog"
	"sync"
	"time"

	"fyne.io/fyne/v2"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"

	"assistant-go/internal/types"
	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// Manager handles PostgreSQL database operations and connections
type Manager struct {
	config *config.Config
	logger *slog.Logger

	// Connection management
	connections map[string]*Connection
	mu          sync.RWMutex

	// Module state
	ctx     context.Context
	cancel  context.CancelFunc
	running bool

	// UI component
	ui *UI
}

// Connection represents a PostgreSQL database connection using pgx
type Connection struct {
	Name     string
	Config   config.DatabaseConnection
	Pool     *pgxpool.Pool
	LastPing time.Time
	Status   ConnectionStatus
	mu       sync.RWMutex
}

// ConnectionStatus represents the status of a database connection
type ConnectionStatus struct {
	Connected bool
	Error     error
	LastCheck time.Time
	Latency   time.Duration
}

// QueryResult represents the result of a database query
type QueryResult struct {
	Columns  []string
	Rows     [][]interface{}
	Error    error
	Duration time.Duration
	RowCount int
}

// NewManager creates a new PostgreSQL manager
func NewManager() *Manager {
	ctx, cancel := context.WithCancel(context.Background())

	return &Manager{
		connections: make(map[string]*Connection),
		ctx:         ctx,
		cancel:      cancel,
		logger:      slog.Default().With("component", "postgres"),
	}
}

// Initialize implements the Module interface
func (m *Manager) Initialize(ctx context.Context, cfg *config.Config) error {
	m.config = cfg
	m.logger.Info("Initializing PostgreSQL manager")

	// Initialize configured connections
	for _, connCfg := range cfg.Database.Connections {
		if connCfg.Enabled {
			if err := m.AddConnection(connCfg); err != nil {
				m.logger.Error("Failed to add connection", "name", connCfg.Name, "error", err)
				// Continue with other connections
			}
		}
	}

	m.logger.Info("PostgreSQL manager initialized", "connections", len(m.connections))
	return nil
}

// Start implements the Module interface
func (m *Manager) Start(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.running {
		return fmt.Errorf("postgres manager is already running")
	}

	m.logger.Info("Starting PostgreSQL manager")

	// Start connection health monitoring
	go m.monitorConnections()

	m.running = true
	m.logger.Info("PostgreSQL manager started successfully")
	return nil
}

// Stop implements the Module interface
func (m *Manager) Stop(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.running {
		return nil
	}

	m.logger.Info("Stopping PostgreSQL manager")

	// Cancel context to stop monitoring
	m.cancel()

	// Close all connections
	for name, conn := range m.connections {
		if err := conn.Close(); err != nil {
			m.logger.Error("Failed to close connection", "name", name, "error", err)
		}
	}

	m.running = false
	m.logger.Info("PostgreSQL manager stopped")
	return nil
}

// Name implements the Module interface
func (m *Manager) Name() string {
	return "postgres"
}

// Health implements the Module interface
func (m *Manager) Health() types.ModuleHealth {
	m.mu.RLock()
	defer m.mu.RUnlock()

	health := types.ModuleHealth{
		Status:    "healthy",
		Message:   fmt.Sprintf("%d connections configured", len(m.connections)),
		LastCheck: time.Now(),
	}

	// Check if any connections are unhealthy
	unhealthyCount := 0
	for _, conn := range m.connections {
		status := conn.GetStatus()
		if !status.Connected {
			unhealthyCount++
		}
	}

	if unhealthyCount > 0 {
		health.Status = "degraded"
		health.Message = fmt.Sprintf("%d of %d connections unhealthy", unhealthyCount, len(m.connections))
	}

	if !m.running {
		health.Status = "unhealthy"
		health.Message = "Module not running"
	}

	return health
}

// AddConnection adds a new database connection
func (m *Manager) AddConnection(cfg config.DatabaseConnection) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if _, exists := m.connections[cfg.Name]; exists {
		return fmt.Errorf("connection %s already exists", cfg.Name)
	}

	conn := &Connection{
		Name:   cfg.Name,
		Config: cfg,
		Status: ConnectionStatus{
			Connected: false,
			LastCheck: time.Now(),
		},
	}

	// Attempt to connect
	if err := conn.Connect(); err != nil {
		m.logger.Error("Failed to connect to database", "name", cfg.Name, "error", err)
		// Store connection even if it failed - user can retry
		conn.Status.Error = err
	}

	m.connections[cfg.Name] = conn
	m.logger.Info("Database connection added", "name", cfg.Name, "connected", conn.Status.Connected)

	return nil
}

// RemoveConnection removes a database connection
func (m *Manager) RemoveConnection(name string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	conn, exists := m.connections[name]
	if !exists {
		return fmt.Errorf("connection %s does not exist", name)
	}

	if err := conn.Close(); err != nil {
		m.logger.Error("Failed to close connection", "name", name, "error", err)
	}

	delete(m.connections, name)
	m.logger.Info("Database connection removed", "name", name)

	return nil
}

// GetConnection returns a database connection by name
func (m *Manager) GetConnection(name string) (*Connection, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	conn, exists := m.connections[name]
	if !exists {
		return nil, fmt.Errorf("connection %s does not exist", name)
	}

	return conn, nil
}

// ListConnections returns all database connections
func (m *Manager) ListConnections() map[string]*Connection {
	m.mu.RLock()
	defer m.mu.RUnlock()

	// Return a copy to prevent external modification
	connections := make(map[string]*Connection)
	for name, conn := range m.connections {
		connections[name] = conn
	}

	return connections
}

// ExecuteQuery executes a SQL query on the specified connection
func (m *Manager) ExecuteQuery(connectionName, query string) (*QueryResult, error) {
	conn, err := m.GetConnection(connectionName)
	if err != nil {
		return nil, err
	}

	return conn.ExecuteQuery(query)
}

// TestConnection tests a database connection using pgx
func (m *Manager) TestConnection(cfg config.DatabaseConnection) error {
	dsn := buildDSN(cfg)

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	conn, err := pgx.Connect(ctx, dsn)
	if err != nil {
		return fmt.Errorf("failed to connect: %w", err)
	}
	defer conn.Close(ctx)

	if err := conn.Ping(ctx); err != nil {
		return fmt.Errorf("failed to ping database: %w", err)
	}

	return nil
}

// monitorConnections monitors the health of all connections
func (m *Manager) monitorConnections() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.checkConnectionHealth()
		}
	}
}

// checkConnectionHealth checks the health of all connections
func (m *Manager) checkConnectionHealth() {
	m.mu.RLock()
	connections := make([]*Connection, 0, len(m.connections))
	for _, conn := range m.connections {
		connections = append(connections, conn)
	}
	m.mu.RUnlock()

	for _, conn := range connections {
		go conn.CheckHealth()
	}
}

// Connect establishes a database connection using pgx pool
func (c *Connection) Connect() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	dsn := buildDSN(c.Config)

	// Configure pool
	config, err := pgxpool.ParseConfig(dsn)
	if err != nil {
		c.Status.Error = err
		c.Status.Connected = false
		return fmt.Errorf("failed to parse config: %w", err)
	}

	// Set pool configuration for better performance
	config.MaxConns = 10
	config.MinConns = 2
	config.MaxConnLifetime = time.Hour
	config.MaxConnIdleTime = 30 * time.Minute

	// Create connection pool
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	start := time.Now()
	pool, err := pgxpool.NewWithConfig(ctx, config)
	if err != nil {
		c.Status.Error = err
		c.Status.Connected = false
		return fmt.Errorf("failed to create connection pool: %w", err)
	}

	// Test the connection
	if err := pool.Ping(ctx); err != nil {
		pool.Close()
		c.Status.Error = err
		c.Status.Connected = false
		return fmt.Errorf("failed to ping database: %w", err)
	}

	c.Pool = pool
	c.Status.Connected = true
	c.Status.Error = nil
	c.Status.LastCheck = time.Now()
	c.Status.Latency = time.Since(start)
	c.LastPing = time.Now()

	return nil
}

// Close closes the database connection pool
func (c *Connection) Close() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.Pool != nil {
		c.Pool.Close()
		c.Pool = nil
		c.Status.Connected = false
	}

	return nil
}

// ExecuteQuery executes a SQL query using pgx
func (c *Connection) ExecuteQuery(query string) (*QueryResult, error) {
	c.mu.RLock()
	pool := c.Pool
	c.mu.RUnlock()

	if pool == nil {
		return nil, fmt.Errorf("connection pool is not established")
	}

	start := time.Now()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	rows, err := pool.Query(ctx, query)
	if err != nil {
		return &QueryResult{
			Error:    err,
			Duration: time.Since(start),
		}, err
	}
	defer rows.Close()

	// Get field descriptions (column info)
	fieldDescriptions := rows.FieldDescriptions()
	columns := make([]string, len(fieldDescriptions))
	for i, fd := range fieldDescriptions {
		columns[i] = string(fd.Name)
	}

	// Read all rows
	var result [][]interface{}
	for rows.Next() {
		values, err := rows.Values()
		if err != nil {
			return &QueryResult{
				Error:    err,
				Duration: time.Since(start),
			}, err
		}

		result = append(result, values)
	}

	if err := rows.Err(); err != nil {
		return &QueryResult{
			Error:    err,
			Duration: time.Since(start),
		}, err
	}

	return &QueryResult{
		Columns:  columns,
		Rows:     result,
		Duration: time.Since(start),
		RowCount: len(result),
	}, nil
}

// CheckHealth checks the health of the connection using pgx
func (c *Connection) CheckHealth() {
	c.mu.RLock()
	pool := c.Pool
	c.mu.RUnlock()

	if pool == nil {
		c.mu.Lock()
		c.Status.Connected = false
		c.Status.Error = fmt.Errorf("connection pool not established")
		c.Status.LastCheck = time.Now()
		c.mu.Unlock()
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	start := time.Now()
	err := pool.Ping(ctx)
	latency := time.Since(start)

	c.mu.Lock()
	c.Status.Connected = (err == nil)
	c.Status.Error = err
	c.Status.LastCheck = time.Now()
	c.Status.Latency = latency
	if err == nil {
		c.LastPing = time.Now()
	}
	c.mu.Unlock()
}

// GetStatus returns the current connection status
func (c *Connection) GetStatus() ConnectionStatus {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.Status
}

// buildDSN builds a PostgreSQL data source name
func buildDSN(cfg config.DatabaseConnection) string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		cfg.Host, cfg.Port, cfg.Username, cfg.Password, cfg.Database, cfg.SSLMode)
}

// GetUI returns the UI component for the postgres module
func (m *Manager) GetUI() fyne.CanvasObject {
	if m.ui == nil {
		// Create cyberpunk theme
		theme := cybertheme.NewCyberTheme()

		// Initialize UI component
		m.ui = NewUI(m, m.config, theme)
	}

	return m.ui.Content()
}
