# Assistant-Go Architecture

This document describes the technical architecture of <PERSON><PERSON><PERSON>, a web-based development assistant built with <PERSON>'s standard library and deployed on Kubernetes.

## System Overview

Assistant-Go follows a microservices architecture deployed on Kubernetes, with each service focused on a specific domain. The system uses PostgreSQL for persistent storage, JWT tokens with Open Policy Agent (OPA) for authentication and authorization, and server-side rendering with real-time updates via WebSocket connections.

```
┌─────────────────────────────────────────────────────────────────┐
│                        Browser Client                            │
│                   (Material Design 3 UI)                         │
└─────────────────────┬───────────────────────────────────────────┘
                      │ HTTPS
┌─────────────────────┴───────────────────────────────────────────┐
│                    Kubernetes Cluster (Kind)                     │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │                    Ingress Controller                        │ │
│ └─────────────────────┬───────────────────────────────────────┘ │
│                       │                                          │
│ ┌─────────────────────┴───────────────────────────────────────┐ │
│ │                    API Gateway Service                       │ │
│ │              (Auth, Rate Limiting, Routing)                  │ │
│ └──────┬──────────┬──────────┬──────────┬────────────────────┘ │
│        │          │          │          │                       │
│ ┌──────┴────┐ ┌──┴────┐ ┌──┴────┐ ┌──┴────┐ ┌──────────────┐ │
│ │   Main    │ │Docker │ │  K8s  │ │Search │ │   Agent      │ │
│ │   API     │ │  API  │ │  API  │ │  API  │ │   Worker     │ │
│ └─────┬─────┘ └───────┘ └───────┘ └───────┘ └──────┬───────┘ │
│       │                                              │          │
│ ┌─────┴──────────────────────────────────────────────┴────────┐ │
│ │                    PostgreSQL (Primary DB)                  │ │
│ └─────────────────────────────────────────────────────────────┘ │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │                    SearXNG Instance                          │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Core Module Architecture

Each module in Assistant-Go is designed with specific responsibilities and clear interfaces. Here's the detailed architecture for all eight core modules:

### 1. AI Agent Module

The AI Agent serves as the intelligent orchestrator that can understand complex tasks and coordinate actions across all other modules. Think of it as the brain of the system that not only responds to queries but actively plans and executes multi-step operations.

```go
// internal/core/agent/interfaces.go
type Agent interface {
// Task planning and execution
PlanTask(ctx context.Context, objective string) (*ExecutionPlan, error)
ExecuteTask(ctx context.Context, plan *ExecutionPlan) (*ExecutionResult, error)

// Learning and adaptation
LearnFromExecution(ctx context.Context, result *ExecutionResult) error
GetSuggestions(ctx context.Context, context WorkContext) ([]Suggestion, error)

// Real-time monitoring
GetActiveOperations(ctx context.Context) ([]Operation, error)
CancelOperation(ctx context.Context, operationID string) error
}

type ExecutionPlan struct {
ID          string
Objective   string
Steps       []Step
Resources   []ResourceRequirement
EstimatedTime time.Duration
}

type Step struct {
Name        string
Module      string // Which module to use
Action      string // What action to perform
Parameters  map[string]interface{}
Dependencies []string // IDs of steps that must complete first
}
```

The agent maintains context across interactions, learning from user patterns and system behaviors to provide increasingly intelligent assistance. It uses a combination of rule-based logic and AI model calls to make decisions.

### 2. PostgreSQL Module

The PostgreSQL module provides comprehensive database management with a focus on developer productivity and query optimization. It goes beyond basic CRUD operations to offer intelligent insights and visual representations of database structures.

```go
// internal/core/postgres/analyzer.go
type QueryAnalyzer interface {
// Query analysis and optimization
AnalyzeQuery(ctx context.Context, query string) (*QueryAnalysis, error)
SuggestIndexes(ctx context.Context, schema Schema, queryPatterns []Query) ([]IndexSuggestion, error)

// Visual explain plans
GenerateExplainPlan(ctx context.Context, query string) (*ExplainPlan, error)
VisualizePlan(plan *ExplainPlan) (*PlanVisualization, error)

// Performance monitoring
GetSlowQueries(ctx context.Context, threshold time.Duration) ([]SlowQuery, error)
GetTableStats(ctx context.Context, table string) (*TableStatistics, error)
}

type QueryAnalysis struct {
Query           string
EstimatedCost   float64
EstimatedRows   int64
IndexesUsed     []string
PotentialIssues []Issue
Optimizations   []Optimization
}
```

### 3. Docker Module

The Docker module provides comprehensive container management with real-time monitoring and visual network topology. It streams live data through WebSocket connections for responsive updates.

```go
// internal/core/docker/monitor.go
type ContainerMonitor interface {
// Real-time monitoring
StreamStats(ctx context.Context, containerID string) (<-chan ContainerStats, error)
StreamLogs(ctx context.Context, containerID string, options LogOptions) (<-chan LogEntry, error)

// Network visualization
GetNetworkTopology(ctx context.Context) (*NetworkTopology, error)
GetContainerConnections(ctx context.Context, containerID string) ([]Connection, error)

// Resource management
GetResourceUsage(ctx context.Context) (*ClusterResources, error)
OptimizeResources(ctx context.Context) ([]ResourceSuggestion, error)
}

type NetworkTopology struct {
Networks   []Network
Containers []ContainerNode
Edges      []NetworkEdge // Connections between containers
}
```

### 4. Kubernetes Module

The Kubernetes module makes cluster management accessible through visual interfaces and simplified workflows. It abstracts complex Kubernetes concepts while still providing full power when needed.

```go
// internal/core/k8s/manager.go
type ClusterManager interface {
// Visual cluster management
GetClusterTopology(ctx context.Context, namespace string) (*ClusterTopology, error)
GetResourceGraph(ctx context.Context, resource ResourceType) (*ResourceGraph, error)

// Simplified operations
DeployApplication(ctx context.Context, spec AppDeploymentSpec) (*Deployment, error)
ScaleDeployment(ctx context.Context, name string, replicas int32) error
RollbackDeployment(ctx context.Context, name string, revision int64) error

// Real-time monitoring
WatchEvents(ctx context.Context, filters EventFilters) (<-chan K8sEvent, error)
GetPodMetrics(ctx context.Context, podName string) (*PodMetrics, error)
}

type AppDeploymentSpec struct {
Name            string
Image           string
Replicas        int32
Resources       ResourceRequirements
HealthCheck     HealthCheckConfig
RolloutStrategy RolloutStrategy
}
```

### 5. Golang Developer Suite

This module provides deep integration with Go development workflows, offering visual representations of performance data and intelligent code analysis.

```go
// internal/core/golang/profiler.go
type GoProfiler interface {
// pprof visualization
ParseProfile(data []byte, profileType ProfileType) (*Profile, error)
GenerateFlameGraph(profile *Profile) (*FlameGraph, error)

// Goroutine analysis
AnalyzeGoroutines(ctx context.Context) (*GoroutineReport, error)
DetectLeaks(ctx context.Context) ([]GoroutineLeak, error)

// Memory profiling
GetHeapProfile(ctx context.Context) (*HeapProfile, error)
FindMemoryLeaks(ctx context.Context) ([]MemoryLeak, error)

// Race detection
AnalyzeRaceConditions(ctx context.Context, binary string) ([]RaceCondition, error)
}

type FlameGraph struct {
Root     *FlameNode
Duration time.Duration
Samples  int64
}

type GoroutineReport struct {
Total      int
Running    int
Blocked    int
Waiting    int
Suspicious []SuspiciousGoroutine
}
```

### 6. SearXNG Search Module

The search module integrates privacy-focused web search with AI-enhanced result processing. It maintains user privacy while providing relevant technical information.

```go
// internal/core/search/engine.go
type SearchEngine interface {
// Privacy-focused search
Search(ctx context.Context, query SearchQuery) (*SearchResults, error)
SearchWithContext(ctx context.Context, query string, context ModuleContext) (*EnhancedResults, error)

// AI enhancement
SummarizeResults(ctx context.Context, results *SearchResults) (*Summary, error)
ExtractCodeSnippets(ctx context.Context, results *SearchResults) ([]CodeSnippet, error)

// Integration with other modules
FindSolutionsForError(ctx context.Context, error string, moduleContext ModuleContext) ([]Solution, error)
}

type SearchQuery struct {
Query      string
Categories []string
TimeRange  TimeRange
Language   string
}

type EnhancedResults struct {
Results    []SearchResult
Summary    string
KeyPoints  []string
Related    []RelatedTopic
}
```

### 7. Task Runner Module

The task module provides visual task execution with dependency management and progress tracking. It supports various task formats and provides detailed execution history.

```go
// internal/core/task/runner.go
type TaskRunner interface {
// Task discovery and parsing
DiscoverTasks(ctx context.Context, source TaskSource) ([]Task, error)
ParseMakefile(content string) ([]Task, error)

// Execution with progress tracking
ExecuteTask(ctx context.Context, taskID string) (*Execution, error)
StreamExecution(ctx context.Context, executionID string) (<-chan ExecutionUpdate, error)

// Dependency management
GetTaskGraph(ctx context.Context) (*TaskGraph, error)
ValidateDependencies(ctx context.Context, taskID string) error

// History and analytics
GetExecutionHistory(ctx context.Context, taskID string) ([]ExecutionRecord, error)
GetTaskMetrics(ctx context.Context, taskID string) (*TaskMetrics, error)
}

type Task struct {
ID           string
Name         string
Description  string
Command      string
Dependencies []string
Environment  map[string]string
Timeout      time.Duration
}

type TaskGraph struct {
Nodes []TaskNode
Edges []DependencyEdge
}
```

### 8. Model Context Protocol (MCP) Module

The MCP module provides transparency into AI model interactions, helping developers understand and debug how AI tools make decisions.

```go
// internal/core/mcp/monitor.go
type MCPMonitor interface {
// Protocol monitoring
InterceptRequest(ctx context.Context, request MCPRequest) (*InterceptedRequest, error)
InterceptResponse(ctx context.Context, response MCPResponse) (*InterceptedResponse, error)

// Visualization
GetFlowDiagram(ctx context.Context, sessionID string) (*FlowDiagram, error)
GetContextWindow(ctx context.Context, sessionID string) (*ContextWindow, error)

// Analysis
AnalyzeToolUsage(ctx context.Context, sessionID string) (*ToolUsageReport, error)
GetDecisionTree(ctx context.Context, sessionID string) (*DecisionTree, error)

// Debugging
ReplaySession(ctx context.Context, sessionID string) (*SessionReplay, error)
ExportSession(ctx context.Context, sessionID string) ([]byte, error)
}

type FlowDiagram struct {
Nodes []FlowNode // Represents different stages
Edges []FlowEdge // Shows data flow
Timeline []Event  // Chronological events
}

type ContextWindow struct {
TotalTokens    int
UsedTokens     int
SystemPrompt   string
Messages       []Message
Tools          []ToolDefinition
}
```

## Module Integration

All modules are designed to work together seamlessly. The AI Agent can orchestrate operations across modules, while each module can also function independently. Here's how they integrate:

```go
// internal/core/integration.go
type ModuleRegistry struct {
agent    agent.Agent
docker   docker.Manager
k8s      k8s.ClusterManager
postgres postgres.Manager
golang   golang.Profiler
search   search.Engine
task     task.Runner
mcp      mcp.Monitor
}

// Example: AI Agent orchestrating across modules
func (r *ModuleRegistry) OptimizeDatabasePerformance(ctx context.Context) error {
// 1. Agent analyzes slow queries
slowQueries, _ := r.postgres.GetSlowQueries(ctx, 1*time.Second)

// 2. Search for optimization strategies
solutions, _ := r.search.FindSolutionsForError(ctx,
"postgresql slow query", postgres.Context{})

// 3. Apply optimizations
for _, solution := range solutions {
r.agent.ExecuteTask(ctx, solution.ToPlan())
}

// 4. Monitor results through MCP
r.mcp.AnalyzeToolUsage(ctx, "db-optimization")

return nil
}
```

## Core Design Principles

### Go Standard Library First

The architecture prioritizes Go's standard library over external frameworks. This approach provides several benefits:

1. **Reduced Dependencies**: Fewer external dependencies mean easier maintenance and better security
2. **Performance**: Direct use of net/http provides excellent performance without framework overhead
3. **Clarity**: Code is explicit and easy to understand without framework magic
4. **Stability**: Standard library APIs are stable and well-documented

### Package Design Philosophy

In Go, packages should be named after what they provide, not what they contain. Each package name should clearly communicate its purpose without needing additional context. This design avoids generic names like "models", "handlers", or "utils" in favor of domain-specific names that make the codebase self-documenting.

### Core Principles for Package Design

1. **Package names are singular, lowercase nouns** that describe what the package provides
2. **Avoid stuttering**: Don't repeat the package name in its exports (e.g., avoid `agent.AgentService`, prefer `agent.Service`)
3. **Small interfaces**: Define interfaces where they're used, not where they're implemented
4. **Domain cohesion**: Keep related functionality together, including types, interfaces, and implementations
5. **No circular dependencies**: Design packages to form a directed acyclic graph

### Comprehensive Package Architecture

```
assistant-go/
├── cmd/                          # Application entry points
│   ├── assistant/               # Main web server binary
│   ├── agentworker/            # Background agent processor
│   └── migrate/                # Database migration tool
│
├── internal/                    # Private application packages
│   │
│   ├── agent/                  # AI agent orchestration domain
│   │   ├── planner/           # Task planning and strategy
│   │   ├── executor/          # Execution engine and coordination
│   │   ├── memory/            # Context and learning storage
│   │   └── langchain/         # LangChain-Go integration
│   │       ├── chains/        # Custom chain implementations
│   │       ├── tools/         # Tool definitions for AI
│   │       └── embeddings/    # Vector storage integration
│   │
│   ├── docker/                 # Container management domain
│   │   ├── container/         # Container lifecycle operations
│   │   ├── image/            # Image management
│   │   ├── network/          # Network topology analysis
│   │   └── stats/            # Resource monitoring
│   │
│   ├── k8s/                   # Kubernetes operations domain
│   │   ├── cluster/          # Cluster-wide operations
│   │   ├── workload/         # Deployment and pod management
│   │   ├── config/           # ConfigMap and Secret handling
│   │   └── observe/          # Monitoring and events
│   │
│   ├── postgres/              # Database operations domain
│   │   ├── connection/       # Connection pool management
│   │   ├── query/            # Query execution and building
│   │   ├── analyze/          # Performance analysis
│   │   ├── schema/           # Schema management
│   │   └── sqlc/             # Generated code from sqlc
│   │
│   ├── goprofiler/            # Go development tools domain
│   │   ├── pprof/            # Profile data processing
│   │   ├── trace/            # Execution trace analysis
│   │   ├── runtime/          # Runtime metrics collection
│   │   └── diagnostic/       # Problem detection algorithms
│   │
│   ├── search/                # Search integration domain
│   │   ├── searxng/          # SearXNG client implementation
│   │   ├── index/            # Local search indexing
│   │   ├── enhance/          # AI-powered enhancements
│   │   └── privacy/          # Privacy protection layer
│   │
│   ├── task/                  # Task execution domain
│   │   ├── discovery/        # Task discovery from sources
│   │   ├── schedule/         # Execution scheduling
│   │   ├── run/              # Runtime execution engine
│   │   └── depend/           # Dependency graph resolution
│   │
│   ├── mcp/                   # Model Context Protocol domain
│   │   ├── protocol/         # Protocol implementation
│   │   ├── intercept/        # Request/response interception
│   │   ├── analyze/          # Usage pattern analysis
│   │   └── visualize/        # Flow visualization logic
│   │
│   ├── cloudflare/            # Cloudflare services integration
│   │   ├── tunnel/           # Cloudflare Tunnel management
│   │   ├── r2/               # R2 storage operations
│   │   ├── access/           # Zero Trust access control
│   │   ├── workers/          # Workers KV integration
│   │   └── analytics/        # Web Analytics integration
│   │
│   ├── auth/                  # Authentication & authorization
│   │   ├── jwt/              # JWT token management
│   │   ├── opa/              # Policy engine integration
│   │   ├── session/          # Session management
│   │   └── rbac/             # Role-based access control
│   │
│   ├── web/                   # Web server layer
│   │   ├── server/           # HTTP server configuration
│   │   ├── router/           # Route registration
│   │   ├── middleware/       # HTTP middleware stack
│   │   ├── websocket/        # WebSocket management
│   │   ├── template/         # Template rendering engine
│   │   └── static/           # Static file serving
│   │
│   ├── integration/           # External service integrations
│   │   ├── claude/           # Claude AI API client
│   │   ├── gemini/           # Google Gemini API client
│   │   ├── github/           # GitHub API integration
│   │   └── slack/            # Slack notifications
│   │
│   └── platform/              # Cross-cutting platform services
│       ├── config/           # Configuration management
│       ├── log/              # Structured logging
│       ├── trace/            # Distributed tracing
│       ├── metric/           # Metrics collection
│       ├── event/            # Event bus for modules
│       └── storage/          # Object storage abstraction
│
├── pkg/                         # Public packages (minimal)
│   ├── aispec/                 # AI provider interface specification
│   └── mcpspec/               # MCP protocol specification
│
├── web/                        # Web assets
│   ├── templates/             # HTML templates by module
│   ├── static/                # CSS, JS, images
│   └── embed.go               # Embedded assets
│
├── migrations/                 # Database migrations
├── deployments/               # Kubernetes manifests
└── tools/                     # Development tools
```

### Layered Architecture Design

The system follows a clear layered architecture that prevents circular dependencies and maintains clean boundaries:

```
┌─────────────────────────────────────────────────────────┐
│                    Web Layer (HTTP)                      │
│  Handles HTTP routing, WebSocket, templates, middleware  │
└────────────────────────┬────────────────────────────────┘
                         │ uses
┌─────────────────────────┴────────────────────────────────┐
│                   Domain Layer                           │
│  agent, docker, k8s, postgres, goprofiler, search, etc.  │
└────────────────────────┬────────────────────────────────┘
                         │ uses
┌─────────────────────────┴────────────────────────────────┐
│                Integration Layer                         │
│     cloudflare, claude, gemini, github, langchain       │
└────────────────────────┬────────────────────────────────┘
                         │ uses
┌─────────────────────────┴────────────────────────────────┐
│                  Platform Layer                          │
│      config, log, trace, metric, event, storage         │
└─────────────────────────────────────────────────────────┘
```

### Domain Package Design Patterns

Each domain package follows consistent internal structure while maintaining domain-specific logic:

#### Agent Domain Example

The agent package orchestrates intelligent operations across all other domains:

```
internal/agent/
├── service.go          # Main service interface and implementation
├── planner/           # Task planning subsystem
│   ├── strategy.go    # Planning strategies
│   ├── optimizer.go   # Plan optimization
│   └── validator.go   # Plan validation
├── executor/          # Execution subsystem
│   ├── runner.go      # Task runner
│   ├── coordinator.go # Multi-domain coordination
│   └── rollback.go    # Failure handling
├── memory/            # Context and learning
│   ├── context.go     # Execution context
│   ├── knowledge.go   # Knowledge base
│   └── pattern.go     # Pattern recognition
└── langchain/         # LangChain-Go integration
    ├── chains.go      # Custom chain definitions
    ├── tools.go       # Tool implementations
    └── prompts.go     # Prompt templates
```

#### Integration with LangChain-Go

The LangChain-Go integration is carefully designed to enhance the agent's capabilities without creating tight coupling:

```
internal/agent/langchain/
├── provider.go         # LangChain provider abstraction
├── chains/
│   ├── planning.go    # Planning chain for task decomposition
│   ├── analysis.go    # Analysis chain for code/query analysis
│   └── summary.go     # Summarization chain for results
├── tools/
│   ├── docker.go      # Docker operations as LangChain tools
│   ├── k8s.go         # Kubernetes operations as tools
│   ├── postgres.go    # Database operations as tools
│   └── search.go      # Search operations as tools
└── memory/
    ├── vector.go      # Vector store for semantic search
    └── conversation.go # Conversation memory management
```

#### Cloudflare Integration Design

The Cloudflare package provides comprehensive integration with Cloudflare's suite of services:

```
internal/cloudflare/
├── client.go          # Shared Cloudflare API client
├── tunnel/
│   ├── manager.go     # Tunnel lifecycle management
│   ├── config.go      # Tunnel configuration
│   └── monitor.go     # Tunnel health monitoring
├── r2/
│   ├── bucket.go      # Bucket operations
│   ├── object.go      # Object storage operations
│   └── presign.go     # Presigned URL generation
├── access/
│   ├── policy.go      # Access policy management
│   ├── identity.go    # Identity provider integration
│   └── audit.go       # Access audit logs
└── workers/
    ├── kv.go          # Workers KV operations
    └── durable.go     # Durable Objects integration
```

### Interface Design Philosophy

Interfaces in Assistant-Go are designed following Go's principle of implicit satisfaction:

1. **Consumer-Defined Interfaces**: The package that uses a service defines what it needs
2. **Small, Focused Interfaces**: Each interface has a single, clear purpose
3. **Composition over Inheritance**: Complex behaviors are built by composing simple interfaces

Example of consumer-defined interfaces:

```go
// In the web package - defines what it needs from agent
package web

type AgentOrchestrator interface {
	PlanExecution(ctx context.Context, objective string) (*ExecutionPlan, error)
	StartExecution(ctx context.Context, planID string) (executionID string, err error)
	StreamProgress(ctx context.Context, executionID string) <-chan Progress
}

// In the agent package - defines what it needs from other domains
package agent

type ContainerManager interface {
	ListContainers(ctx context.Context) ([]Container, error)
	ExecuteInContainer(ctx context.Context, id string, cmd []string) (output string, err error)
}

type DatabaseAnalyzer interface {
	AnalyzeQuery(ctx context.Context, query string) (*QueryPlan, error)
	SuggestOptimizations(ctx context.Context, plan *QueryPlan) ([]Optimization, error)
}
```

### Module Communication Patterns

Modules communicate through well-defined patterns that maintain loose coupling:

#### Event-Driven Communication

The platform provides an event bus for asynchronous communication between modules:

```
platform/event/
├── bus.go             # Event bus implementation
├── types.go           # Common event types
└── subscription.go    # Subscription management
```

Modules can publish and subscribe to events without direct dependencies:
- Agent publishes execution progress events
- Web layer subscribes to display real-time updates
- Modules remain decoupled through the event bus

#### Service Registry Pattern

Modules register their capabilities at startup, allowing dynamic discovery:

```
platform/registry/
├── registry.go        # Service registry
├── health.go          # Health checking
└── discovery.go       # Service discovery
```

This enables the agent to dynamically discover available tools and capabilities without hard-coding dependencies.

### Configuration Management

Configuration is centralized but allows module-specific sections:

```yaml
# config/assistant.yaml
server:
  port: 8080
  read_timeout: 30s

agent:
  max_concurrent_tasks: 5
  planning_timeout: 30s
  langchain:
    model: "claude-3-opus"
    temperature: 0.7

cloudflare:
  account_id: "${CLOUDFLARE_ACCOUNT_ID}"
  tunnel:
    enabled: true
    name: "assistant-go"
  r2:
    bucket: "assistant-storage"

postgres:
  max_connections: 25
  idle_connections: 5
  connection_timeout: 10s
```

### Testing Strategy

Each package maintains its own tests with clear boundaries:

```
internal/agent/
├── service_test.go       # Unit tests
├── integration_test.go   # Integration tests
└── testdata/            # Test fixtures
```

Testing principles:
- Unit tests mock external dependencies through interfaces
- Integration tests use real implementations where possible
- End-to-end tests verify complete user scenarios

## HTTP Server Architecture

### Request Flow

Each HTTP request follows this flow through the system:

```go
// 1. Request arrives at custom ServeMux wrapper
type Server struct {
mux    *http.ServeMux
logger *slog.Logger
tracer trace.Tracer
}

func (s *Server) ServeHTTP(w http.ResponseWriter, r *http.Request) {
// Wrap response writer for observability
wrapped := &responseWriter{ResponseWriter: w}

// Start trace span
ctx, span := s.tracer.Start(r.Context(), "http.request")
defer span.End()

// Log request
s.logger.InfoContext(ctx, "request started",
"method", r.Method,
"path", r.URL.Path,
"remote", r.RemoteAddr,
)

// Serve request
s.mux.ServeHTTP(wrapped, r.WithContext(ctx))

// Log response
s.logger.InfoContext(ctx, "request completed",
"status", wrapped.status,
"bytes", wrapped.bytes,
)
}
```

### Middleware Chain

Middleware is implemented as standard HTTP handlers:

```go
// Authentication middleware
func RequireAuth(next http.HandlerFunc) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        token := extractToken(r)
        if token == "" {
            http.Error(w, "Unauthorized", http.StatusUnauthorized)
            return
        }
        
        claims, err := validateJWT(token)
        if err != nil {
            http.Error(w, "Invalid token", http.StatusUnauthorized)
            return
        }
        
        // Add claims to context
        ctx := context.WithValue(r.Context(), "claims", claims)
        next.ServeHTTP(w, r.WithContext(ctx))
    }
}

// OPA authorization middleware
func RequirePermission(action string) func(http.HandlerFunc) http.HandlerFunc {
    return func(next http.HandlerFunc) http.HandlerFunc {
        return func(w http.ResponseWriter, r *http.Request) {
            claims := r.Context().Value("claims").(Claims)
            
            allowed, err := checkOPAPolicy(r.Context(), claims, action, r)
            if err != nil || !allowed {
                http.Error(w, "Forbidden", http.StatusForbidden)
                return
            }
            
            next.ServeHTTP(w, r)
        }
    }
}
```

## Template Rendering

The system uses Go's html/template with templ UI components for server-side rendering:

```go
// Base template structure
type PageData struct {
    Title       string
    User        *User
    CSRFToken   string
    Theme       string // "light" or "dark"
    Content     interface{}
}

// Template rendering with templ UI
func (h *Handler) RenderTemplate(w http.ResponseWriter, r *http.Request, name string, data PageData) {
    tmpl, err := h.templates.Get(name)
    if err != nil {
        h.logger.Error("template not found", "name", name, "error", err)
        http.Error(w, "Internal Server Error", http.StatusInternalServerError)
        return
    }
    
    // Add common data
    data.CSRFToken = csrf.Token(r)
    data.User = getUserFromContext(r.Context())
    data.Theme = getThemePreference(r)
    
    // Execute template
    var buf bytes.Buffer
    if err := tmpl.Execute(&buf, data); err != nil {
        h.logger.Error("template execution failed", "error", err)
        http.Error(w, "Internal Server Error", http.StatusInternalServerError)
        return
    }
    
    w.Header().Set("Content-Type", "text/html; charset=utf-8")
    w.Write(buf.Bytes())
}
```

## Real-time Updates

WebSocket connections provide real-time updates for dynamic content:

```go
// WebSocket handler for container logs
func (h *Handler) StreamContainerLogs(w http.ResponseWriter, r *http.Request) {
    containerID := chi.URLParam(r, "id")
    
    // Upgrade to WebSocket
    conn, err := h.upgrader.Upgrade(w, r, nil)
    if err != nil {
        return
    }
    defer conn.Close()
    
    // Stream logs
    ctx, cancel := context.WithCancel(r.Context())
    defer cancel()
    
    logStream, err := h.docker.ContainerLogs(ctx, containerID)
    if err != nil {
        conn.WriteJSON(map[string]string{"error": err.Error()})
        return
    }
    
    // Forward logs to WebSocket
    scanner := bufio.NewScanner(logStream)
    for scanner.Scan() {
        if err := conn.WriteJSON(map[string]string{
            "log": scanner.Text(),
            "timestamp": time.Now().Format(time.RFC3339),
        }); err != nil {
            return
        }
    }
}
```

## Database Design

PostgreSQL schema uses sqlc for type-safe queries:

```sql
-- users table with RBAC support
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    password_hash TEXT NOT NULL,
    role TEXT NOT NULL DEFAULT 'user',
    theme_preference TEXT DEFAULT 'light',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Multi-tenancy through workspace isolation
CREATE TABLE workspaces (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    owner_id UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE workspace_members (
    workspace_id UUID NOT NULL REFERENCES workspaces(id),
    user_id UUID NOT NULL REFERENCES users(id),
    role TEXT NOT NULL DEFAULT 'member',
    PRIMARY KEY (workspace_id, user_id)
);

-- Saved queries with workspace isolation
CREATE TABLE saved_queries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workspace_id UUID NOT NULL REFERENCES workspaces(id),
    name TEXT NOT NULL,
    query TEXT NOT NULL,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

## Authentication & Authorization

### JWT Token Structure

```go
type Claims struct {
    UserID      string   `json:"user_id"`
    Email       string   `json:"email"`
    Role        string   `json:"role"`
    WorkspaceID string   `json:"workspace_id"`
    Permissions []string `json:"permissions"`
    jwt.RegisteredClaims
}
```

### OPA Policy Example

```rego
package assistant.authz

default allow = false

# Allow users to access their own workspace
allow {
    input.method == "GET"
    input.path[0] == "api"
    input.path[1] == "workspaces"
    input.path[2] == input.claims.workspace_id
}

# Allow workspace owners full access
allow {
    input.claims.role == "owner"
    input.path[0] == "api"
    input.path[1] == "workspaces"
    input.path[2] == input.claims.workspace_id
}

# Allow read-only access for members
allow {
    input.claims.role == "member"
    input.method == "GET"
}
```

## Observability

### Structured Logging with slog

```go
// Logger wrapper for consistent formatting
type Logger struct {
    *slog.Logger
}

func NewLogger(w io.Writer) *Logger {
    handler := slog.NewJSONHandler(w, &slog.HandlerOptions{
        Level: slog.LevelInfo,
        ReplaceAttr: func(groups []string, a slog.Attr) slog.Attr {
            // Add service metadata
            if a.Key == slog.TimeKey {
                return slog.String("timestamp", a.Value.Time().Format(time.RFC3339))
            }
            return a
        },
    })
    
    logger := slog.New(handler).With(
        "service", os.Getenv("SERVICE_NAME"),
        "version", os.Getenv("SERVICE_VERSION"),
    )
    
    return &Logger{logger}
}
```

### Distributed Tracing

OpenTelemetry integration for Jaeger:

```go
func InitTracer(ctx context.Context, serviceName string) (*sdktrace.TracerProvider, error) {
    exporter, err := jaeger.New(
        jaeger.WithCollectorEndpoint(
            jaeger.WithEndpoint(os.Getenv("JAEGER_ENDPOINT")),
        ),
    )
    if err != nil {
        return nil, err
    }
    
    tp := sdktrace.NewTracerProvider(
        sdktrace.WithBatcher(exporter),
        sdktrace.WithResource(resource.NewWithAttributes(
            semconv.SchemaURL,
            semconv.ServiceNameKey.String(serviceName),
        )),
    )
    
    otel.SetTracerProvider(tp)
    return tp, nil
}
```

### Frontend Template Architecture with Templ

Assistant-Go uses Templ instead of Go's standard html/template for compelling reasons:

#### Why Templ?

Templ provides type-safe, compiled templates that catch errors at compile time rather than runtime. This aligns perfectly with Go's philosophy of catching errors early and explicitly.

```
internal/web/templates/
├── layout/
│   ├── base.templ         # Base HTML structure
│   ├── nav.templ          # Navigation components
│   └── theme.templ        # Theme switching logic
├── components/            # Reusable UI components
│   ├── button.templ       # TemplUI button wrapper
│   ├── card.templ         # Card component
│   ├── modal.templ        # Modal dialogs
│   └── form/              # Form components
│       ├── input.templ
│       ├── select.templ
│       └── textarea.templ
├── agent/                 # Agent module templates
├── docker/                # Docker module templates
└── shared/                # Shared components
```

#### TemplUI Integration

TemplUI provides a comprehensive component library designed specifically for Templ:

Benefits of using TemplUI:
- Consistent Material Design 3 components out of the box
- Fully customizable with Tailwind CSS
- Accessibility built-in with ARIA attributes
- Dark mode support aligns with our requirements
- Type-safe component props

The integration approach:
1. TemplUI components are wrapped in our own components for consistency
2. Custom styling is applied through Tailwind classes
3. Component behavior is enhanced with Alpine.js where needed
4. All components follow the blue/white color scheme with dark mode support

#### Template Compilation Strategy

Templates are compiled as part of the build process:

```
build process:
1. templ generate - Compiles .templ files to .go files
2. go generate - Runs any other code generation
3. go build - Builds the final binary with embedded templates
```

This ensures that template errors are caught during development, not in production.

## Security Considerations

1. **CSRF Protection**: All state-changing requests require CSRF tokens
2. **Content Security Policy**: Strict CSP headers prevent XSS attacks
3. **Rate Limiting**: Per-user rate limits prevent abuse
4. **Input Validation**: All user input is validated and sanitized
5. **Secure Headers**: HSTS, X-Frame-Options, etc. are properly configured

## Performance Optimizations

1. **Template Caching**: Compiled templates are cached in production
2. **Connection Pooling**: Database connections are pooled and reused
3. **Static Asset Embedding**: CSS/JS files are embedded in the binary
4. **Query Optimization**: N+1 queries are avoided through careful design
5. **Graceful Degradation**: WebSocket fallback to SSE for compatibility